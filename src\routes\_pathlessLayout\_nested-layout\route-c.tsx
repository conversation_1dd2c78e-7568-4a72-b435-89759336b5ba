import { createFileRoute } from '@tanstack/react-router'
import { Instrument } from 'piano-chart'
import { useCallback } from 'react'
import { PianoDiagram } from '~/components/PianoDiagram'

export const Route = createFileRoute('/_pathlessLayout/_nested-layout/route-c')(
  {
    component: RouteComponent,
  },
)

function RouteComponent() {
    const setPianoChartContainer = useCallback((el: HTMLDivElement) => {
        const piano = new Instrument(el, {
            startOctave: 3, endOctave: 5,
            keyPressStyle: 'vivid',
        })
        piano.create()
        piano.keyDown('D4')
        piano.keyDown('F#4')
        piano.keyDown('A4')
    }, [])

  return <div ref={setPianoChartContainer}>
    <PianoDiagram/>
  </div>
}
