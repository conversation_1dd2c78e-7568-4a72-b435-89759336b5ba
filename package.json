{"name": "tanstack-start-example-basic", "private": true, "sideEffects": false, "type": "module", "scripts": {"dev": "vite dev", "build": "vite build && tsc --noEmit", "start": "node .output/server/index.mjs"}, "dependencies": {"@mdx-js/react": "^3.1.0", "@tailwindcss/vite": "^4.1.12", "@tanstack/react-router": "^1.131.27", "@tanstack/react-router-devtools": "^1.131.27", "@tanstack/react-start": "^1.131.27", "piano-chart": "^1.5.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.6.0", "tonal": "^6.4.2", "zod": "^3.24.2"}, "devDependencies": {"@mdx-js/rollup": "^3.1.0", "@types/node": "^22.5.4", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss": "^4.1.12", "typescript": "^5.7.2", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}}