{"files.watcherExclude": {"**/routeTree.gen.ts": true}, "search.exclude": {"**/routeTree.gen.ts": true}, "files.readonlyInclude": {"**/routeTree.gen.ts": true}, "editor.defaultFormatter": "esbenp.prettier-vscode", "tailwindCSS.experimental.classRegex": ["tv\\((?:[^)]*\\{[^)]*base:\\s*['\"]([^'\"]*)['\"])", "tv\\(.*variants:\\s*\\{[^}]*['\"](?:[^'\"]+)['\"]:\\s*['\"]([^'\"]*)['\"]", "clsx\\(([^)]*)\\)", "className=['\"`]([^'\"]*)['\"`]"], "tailwindCSS.includeLanguages": {"html": "html", "javascript": "javascript", "css": "css"}, "editor.quickSuggestions": {"strings": true}, "tailwindCSS.experimental.configFile": "./src/styles/index.css"}