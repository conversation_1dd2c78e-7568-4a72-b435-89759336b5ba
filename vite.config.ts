import mdx from '@mdx-js/rollup'
import tailwindcss from '@tailwindcss/vite'
import { tanstackStart } from '@tanstack/react-start/plugin/vite'
import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'
import tsConfigPaths from 'vite-tsconfig-paths'

export default defineConfig({
  server: {
    port: 3000,
  },
  plugins: [
    // Have to run before @vitejs/plugin-react: https://mdxjs.com/docs/getting-started/
    {enforce: 'pre', ...mdx(),},
    tsConfigPaths({
      projects: ['./tsconfig.json'],
    }),
    tanstackStart({ customViteReactPlugin: true }),
    tailwindcss(),
    react({include: /\.(jsx|js|mdx|md|tsx|ts)$/}),
  ],
})
